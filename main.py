from flask import Flask, render_template, jsonify, request, session, redirect, url_for
from random import randint
from emailaccess import get_metadata, get_subject, get_authorization_url, handle_oauth_callback, is_authenticated, logout
from safetensors.torch import load_file
from transformers import RobertaConfig, RobertaForSequenceClassification, RobertaTokenizer
import torch
import pandas as pd
import threading
import vtotala
from transformers import TextClassificationPipeline
import tldextract
from urllib.parse import urlparse, urlunparse
# from outlooktest import get_data, get_sub
from pycaret.classification import load_model, predict_model
import os
from huggingface_hub import snapshot_download
from transformers import AutoModelForSequenceClassification, AutoTokenizer
import sys


if getattr(sys, 'frozen', False):
    base_path = sys._MEIPASS
else:
    base_path = os.path.abspath(".")

# Load from Hugging Face
repo_id = "itsjustahobby/athenav3-model"
model_dir = snapshot_download(repo_id=repo_id)

tokenizer = AutoTokenizer.from_pretrained(model_dir)
model = AutoModelForSequenceClassification.from_pretrained(model_dir)
pipe = TextClassificationPipeline(model=model, tokenizer=tokenizer, device=-1, truncation=True, return_all_scores=False)

'''def get_recent_emails(n, email_provider):
    return [{'id': i, 'subject': f'Email {i}'} for i in range(1, n + 1)]
'''

id2label = model.config.id2label

def get_recent_emails(n, email_provider):
    if email_provider == "gmail": # enters this if condition 
        return get_subject(n)
    elif email_provider == "outlook":
        return [{'id': i, 'subject': f'{get_sub(i)}'} for i in range(1, n + 1)]
    else:
        print("Unsupported email provider.")
        return []

def clean_email_text(text):
    """Clean email text to remove HTML, headers, and other noise that might confuse the model"""
    import re

    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)

    # Remove email headers (common patterns)
    text = re.sub(r'From:.*?\n', '', text, flags=re.IGNORECASE)
    text = re.sub(r'To:.*?\n', '', text, flags=re.IGNORECASE)
    text = re.sub(r'Subject:.*?\n', '', text, flags=re.IGNORECASE)
    text = re.sub(r'Date:.*?\n', '', text, flags=re.IGNORECASE)
    text = re.sub(r'Content-Type:.*?\n', '', text, flags=re.IGNORECASE)

    # Remove excessive whitespace and newlines
    text = re.sub(r'\n+', ' ', text)
    text = re.sub(r'\s+', ' ', text)

    # Remove URLs (they might bias the model)
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '[URL]', text)

    return text.strip()

def seText(input_text):
    if(input_text == ""):
        return "N/A, no body present"

    # Clean the text first
    cleaned_text = clean_email_text(input_text)

    # If text is too short after cleaning, it might not be meaningful
    if len(cleaned_text.strip()) < 10:
        return "N/A, insufficient content for analysis"

    device = torch.device("cpu")
    model.to(device)
    inputs = tokenizer(cleaned_text, return_tensors="pt", truncation=True, max_length=512).to(device)
    with torch.no_grad():
        outputs = model(**inputs)

    logits = outputs.logits
    probs = torch.nn.functional.softmax(logits, dim=-1)
    predicted_label = torch.argmax(probs, dim=1).item()
    confidence = probs[0][predicted_label].item() * 100

    # Debug: Print all probabilities to understand model behavior
    print(f"Debug - Original text preview: {input_text[:100]}...")
    print(f"Debug - Cleaned text preview: {cleaned_text[:100]}...")
    print(f"Debug - All probabilities: {probs[0].tolist()}")
    print(f"Debug - Predicted label: {id2label[predicted_label]}")
    print(f"Debug - Confidence: {confidence:.2f}%")

    # Add uncertainty for borderline cases
    if 85 <= confidence <= 95:
        return f"{id2label[predicted_label]} {confidence:.2f}% (Medium Confidence)"
    elif confidence < 70:
        return f"{id2label[predicted_label]} {confidence:.2f}% (Low Confidence)"
    else:
        return f"{id2label[predicted_label]} {confidence:.2f}%"


def get_root_domain(url):
    # Parse the URL to get the domain information
    parsed_url = urlparse(url)
    
    # Use tldextract to separate the domain, subdomain, and TLD
    extracted = tldextract.extract(parsed_url.netloc)
    
    # Construct the normalized root domain
    root_domain = f"{extracted.domain}.{extracted.suffix}"
    
    return root_domain



def simplify_url(url):
    # Parse the URL
    parsed_url = urlparse(url)
    
    # Retain only the scheme (http/https) and the main domain
    domain_info = tldextract.extract(url)
    simplified_domain = domain_info.registered_domain
    
    # Remove any path or query details
    simplified_url = urlunparse((parsed_url.scheme, simplified_domain, '', '', '', ''))
    
    return simplified_url


# Removed linkProcess function - now using only VirusTotal for link analysis


def linkVt(links):
    results = []
    counts = {'malicious': 0, 'suspicious': 0, 'undetected': 0, 'harmless': 0, 'timeout': 0, "N/A": 0}
    for link in links:
        response = vtotala.report(link)
        print(response)
        max_key = max(response, key=response.get)
        max_value = response[max_key]
        if(max_value == 0):
            max_key = "N/A"
            results.append(max_key)
        else:
            # Calculate the total value
            total_value = sum(response.values())

            # Calculate the percentage composition
            percentage_composition = (max_value / total_value) * 100 if total_value > 0 else 0

            results.append(max_key + " " + str(percentage_composition) + "%")
        counts[max_key] += 1

    return results, max(counts, key=counts.get)

analysis_result = None
analysis_ready = threading.Event()

def analyze_data(service, email_id, gmail_credentials=None):
    global analysis_result
    global analysis_ready

    try:
        if service == "gmail":
            # Use the passed credentials for Gmail
            if gmail_credentials:
                # Temporarily store credentials in a way the emailaccess functions can use them
                from emailaccess import get_metadata_with_creds
                body, links = get_metadata_with_creds(int(email_id), gmail_credentials)
            else:
                print("Error: No Gmail credentials provided for analysis")
                analysis_result = {'error': 'Authentication required'}
                analysis_ready.set()
                return
        elif service == "outlook":
            body, links = get_data(int(email_id))
        else:
            print(f"Unsupported service: {service}")
            analysis_result = {'error': 'Unsupported email service'}
            analysis_ready.set()
            return

        raw_body = body
        body = "VERDICT: " + seText(body)

        # Only use VirusTotal for link analysis (removed in-house model)
        vtr, vtv = linkVt(links)
        vtr = [f"{link}\n\nVERDICT: {vtr[i]}" for i, link in enumerate(links)]

        # Determine overall link verdict based on VirusTotal results only
        if vtv == "malicious":
            linkv = "Malicious"
        elif vtv == "suspicious":
            linkv = "Suspicious"
        elif vtv == "harmless":
            linkv = "Safe"
        else:
            linkv = "Unknown"

        # Format links with VirusTotal verdicts only
        links = [f"{link}\n\nVERDICT: {vtr[i]}" for i, link in enumerate(links)]
        analysis_result = {'body': body, 'rawbody':raw_body, 'links': links, 'linkv': linkv, 'linkvt': vtv, 'vtl': vtr}
        analysis_ready.set()
    except Exception as e:
        print(f"Error in analyze_data: {e}")
        analysis_result = {'error': str(e)}
        analysis_ready.set()

app = Flask('app')
app.config['DEBUG'] = True
app.secret_key = 'poopenfarten'
app.template_folder = os.path.join(base_path, 'templates')

# Configure session for OAuth2
app.config['SESSION_COOKIE_SECURE'] = False  # Allow HTTP for development
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

@app.route('/')
def page1():
    return render_template('hi.html')

@app.route('/auth/gmail')
def auth_gmail():
    """Initiate Gmail OAuth2 authentication"""
    try:
        authorization_url = get_authorization_url()
        return redirect(authorization_url)
    except Exception as e:
        print(f"Error initiating Gmail auth: {e}")
        return redirect(url_for('page1'))

@app.route('/auth/callback')
def auth_callback():
    """Handle OAuth2 callback from Google"""
    try:
        authorization_response = request.url
        state = request.args.get('state')

        handle_oauth_callback(authorization_response, state)

        # Redirect to emails page after successful authentication
        session['email_service'] = 'gmail'
        return redirect(url_for('email_list'))
    except Exception as e:
        print(f"Error handling OAuth callback: {e}")
        return redirect(url_for('page1'))

@app.route('/auth/logout')
def auth_logout():
    """Logout from Gmail"""
    logout()
    return redirect(url_for('page1'))

@app.route('/auth/status')
def auth_status():
    """Check authentication status (for debugging)"""
    return jsonify({
        'authenticated': is_authenticated(),
        'has_credentials': 'gmail_credentials' in session,
        'session_keys': list(session.keys())
    })

'''
@app.route('/emails', methods=['GET', 'POST'])
def email_list():
    if request.method == 'POST':
        email_service = request.form.get('email_service')  # Get the email service selected
        session['email_service'] = email_service
        # Retrieve the recent 20 emails
        print(email_service)
        recent_emails = get_recent_emails(10, email_service)
    else:
        try:
            # Retrieve the recent 20 emails
            recent_emails = get_recent_emails(10, email_service)
        except:
            recent_emails = []

    return render_template('emails.html', emails=recent_emails)'''
@app.route('/emails', methods=['GET', 'POST'])
def email_list():
    # Retrieve the email service from the session, or use a default value if not set
    email_service = session.get('email_service', 'default_service')  # Use a default service if not in session

    if request.method == 'POST':
        email_service = request.form.get('email_service')  # Get the email service selected
        session['email_service'] = email_service  # Store the selected email service in session

    # Check if Gmail is selected and user is not authenticated
    if email_service == 'gmail' and not is_authenticated():
        return redirect(url_for('auth_gmail'))

    try:
        # Retrieve the recent 10 emails from the selected service
        recent_emails = get_recent_emails(5, email_service)
    except:
        recent_emails = []

    return render_template('emails.html', emails=recent_emails)



@app.route('/start_analysis', methods=['POST'])
def start_analysis():
    global analysis_result
    global analysis_ready

    data = request.json
    email_id = data['email_id']
    email_service = session.get('email_service')

    # Get Gmail credentials from session if needed
    gmail_credentials = None
    if email_service == 'gmail':
        gmail_credentials = session.get('gmail_credentials')
        if not gmail_credentials:
            return jsonify({'status': 'error', 'message': 'Gmail authentication required'})

    analysis_result = None
    analysis_ready.clear()
    threading.Thread(target=analyze_data, args=(email_service, email_id, gmail_credentials)).start()
    return jsonify({'status': 'Analysis started'})

@app.route('/check_status')
def check_status():
    if analysis_ready.is_set():
        return jsonify({'status': 'done', 'result': analysis_result})
    else:
        return jsonify({'status': 'pending'})

@app.route('/analysis')
def analysis():
    return render_template('loading.html')

@app.errorhandler(404)
def error404(e):
    return render_template('404.html'), 404

app.run(host='0.0.0.0', port=5000)
