import requests
import base64
from urllib.parse import urlparse
import os

api_key = "d20df7ddd7f1d2f3af0bfef55e3b9e876d2b47fd953cacd768bf16c5b4ec24f6"

def report(url):
    try:
        # Parse the domain from the URL
        parsed_url = urlparse(url)
        domain = parsed_url.netloc  # Get only the domain name (e.g., example.com)

        # The URL for the VirusTotal API endpoint
        endpoint = f'https://www.virustotal.com/api/v3/domains/{domain}'

        # Headers with the API key
        headers = {
            'x-apikey': api_key
        }

        # Send the request to get the domain report
        response = requests.get(endpoint, headers=headers)

        # Debug information
        print(f"VirusTotal API - Checking domain: {domain}")
        print(f"VirusTotal API - Response status: {response.status_code}")

        # Check the response
        if response.status_code == 200:
            data = response.json()

            # Initialize counts
            verdict_counts = {
                'malicious': 0,
                'suspicious': 0,
                'undetected': 0,
                'harmless': 0,
                'timeout': 0
            }

            # Access the results
            results = data.get('data', {}).get('attributes', {}).get('last_analysis_results', {})

            # Iterate through the results
            for engine, result in results.items():
                verdict = result.get('category', 'unknown')
                if verdict == 'malicious':
                    verdict_counts['malicious'] += 1
                elif verdict == 'suspicious':
                    verdict_counts['suspicious'] += 1
                elif verdict == 'undetected':
                    verdict_counts['undetected'] += 1
                elif verdict == 'harmless':
                    verdict_counts['harmless'] += 1
                elif verdict == 'timeout':
                    verdict_counts['timeout'] += 1

            print(f"VirusTotal API - Verdict counts: {verdict_counts}")
            return verdict_counts
        else:
            print(f"VirusTotal API - Error response: {response.status_code}")
            print(f"VirusTotal API - Error text: {response.text}")
            return {
                'malicious': 0,
                'suspicious': 0,
                'undetected': 0,
                'harmless': 0,
                'timeout': 0
            }
    except Exception as e:
        print(f"Error: {e}")
        return {
            'malicious': 0,
            'suspicious': 0,
            'undetected': 0,
            'harmless': 0,
            'timeout': 0
        }
