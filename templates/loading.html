<!--<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading...</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div id="loading">
        <h1>Loading...</h1>
    </div>
    <div id="content" style="display: none;">
        <h2>Body:</h2>
        <h3 id="bodyText"></h3>
        <h2>Link detection model:</h2>
        <h3 id="linkv"></h3>
        <button id="toggleLinksButton">Show Links</button>
        <ul id="linksList">
        </ul>
        <h2>VirusTotal:</h2>
        <h3 id="linkvt"></h3>
        <button id="toggleLinksvButton">Show Virus Total Links</button>
        <ul id="vtlinksList">
        </ul>
        <button id="backButton" style="display: none;">Back to Email List</button>
    </div>
    <script>
        async function checkStatus() {
            try {
                const response = await fetch('/check_status');
                const data = await response.json();
                if (data.status === 'done') {
                    document.getElementById('loading').style.display = 'none';

                    // Populate the body text and list
                    document.getElementById('bodyText').innerText = data.result.body;
                    document.getElementById('linkv').innerText = "VERDICT: " + data.result.linkv;
                    document.getElementById('linkvt').innerText = "VERDICT: " + data.result.linkvt;

                    const linksList = document.getElementById('linksList');
                    const vtlinksList = document.getElementById('vtlinksList'); 
                    linksList.innerHTML = data.result.links.map(link => {
                        const [part1, part2] = link.split("\n\n");
                        return `
                            <li>
                                <span class="link-part1">${part1}</span>
                                <span class="link-part2">${part2}</span>
                            </li>`;
                    }).join('');
                    
                    vtlinksList.innerHTML = data.result.vtl.map(vtlink => {
                        const [part1, part2] = vtlink.split("\n\n");
                        return `
                            <li>
                                <span class="link-part1">${part1}</span>
                                <span class="link-part2">${part2}</span>
                            </li>`;
                    }).join('');
                    
                    


                    document.getElementById('content').style.display = 'block';
                    linksList.style.display = 'none';
                    vtlinksList.style.display = 'none';
                    document.getElementById('backButton').style.display = 'block'; // Show the back button
                } else {
                    setTimeout(checkStatus, 1000); // Poll every second
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        document.getElementById('toggleLinksButton').addEventListener('click', () => {
            const linksList = document.getElementById('linksList');
            if (linksList.style.display === 'none') {
                linksList.style.display = 'block';
                document.getElementById('toggleLinksButton').innerText = 'Hide Links';
            } else {
                linksList.style.display = 'none';
                document.getElementById('toggleLinksButton').innerText = 'Show Links';
            }
        });

        document.getElementById('toggleLinksvButton').addEventListener('click', () => {
            const vtlinksList = document.getElementById('vtlinksList'); // Fixed ID here
            if (vtlinksList.style.display === 'none') {
                vtlinksList.style.display = 'block';
                document.getElementById('toggleLinksvButton').innerText = 'Hide Links';
            } else {
                vtlinksList.style.display = 'none';
                document.getElementById('toggleLinksvButton').innerText = 'Show Links';
            }
        });

        document.getElementById('backButton').addEventListener('click', () => {
            window.location.href = '/emails'; // Redirect to the email list page
        });

        window.onload = checkStatus;
    </script>
</body>
</html>-->
<!--<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading...</title>
    <link href="https://unpkg.com/flowbite@1.5.1/dist/flowbite.min.css" rel="stylesheet" />
    <style>
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #content {
            max-height: 80vh; /* Set max height for scrolling */
            overflow-y: auto; /* Enable vertical scrolling */
        }

        .link-item {
            word-wrap: break-word; /* Ensure links wrap to the next line */
        }
    </style>
</head>

<body class="bg-gray-200 h-screen flex items-center justify-center overflow-y-auto">
    <div id="loading" class="text-center bg-white p-6 border border-gray-300 rounded-lg shadow-md">
        <div class="spinner"></div>
        <h1 class="text-3xl font-semibold text-gray-700">Loading...</h1>
    </div>

    <div id="content" style="display: none;" class="p-6 w-full max-w-4xl bg-white border border-gray-200 rounded-lg shadow-lg">
        <h2 class="text-xl font-bold mb-2">Body:</h2>
        <h3 id="bodyText" class="mb-4 text-gray-600"></h3>

        <h2 class="text-xl font-bold mb-2">Link detection model:</h2>
        <h3 id="linkv" class="mb-4 text-gray-600"></h3>

        <h2 class="text-xl font-bold mb-2">VirusTotal:</h2>
        <h3 id="linkvt" class="mb-4 text-gray-600"></h3>

        <button id="toggleLinksButton" class="block w-full text-center bg-purple-600 text-white py-2 px-4 mb-4 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium transition duration-150">
            Show Links
        </button>
        <ul id="linksList" class="list-disc list-inside bg-gray-100 p-4 rounded-lg max-h-64 overflow-y-auto" style="display: none;">
        </ul>

        <button id="toggleLinksvButton" class="block w-full text-center bg-purple-600 text-white py-2 px-4 mb-4 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium transition duration-150">
            Show Virus Total Links
        </button>
        <ul id="vtlinksList" class="list-disc list-inside bg-gray-100 p-4 rounded-lg max-h-64 overflow-y-auto" style="display: none;">
        </ul>

        <button id="backButton" style="display: none;" class="block w-full text-center bg-purple-600 text-white py-2 px-4 mb-4 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium transition duration-150">
            Back to Email List
        </button>
    </div>

    <script>
        async function checkStatus() {
            try {
                const response = await fetch('/check_status');
                const data = await response.json();
                if (data.status === 'done') {
                    document.getElementById('loading').style.display = 'none';

                    // Populate the body text and list
                    document.getElementById('bodyText').innerText = data.result.body;
                    document.getElementById('linkv').innerText = "VERDICT: " + data.result.linkv;
                    document.getElementById('linkvt').innerText = "VERDICT: " + data.result.linkvt;

                    const linksList = document.getElementById('linksList');
                    const vtlinksList = document.getElementById('vtlinksList');
                    linksList.innerHTML = data.result.links.map(link => {
                        const [part1, part2] = link.split("\n\n");
                        return `
                            <li class="link-item mb-2">
                                <span class="font-semibold text-blue-600">${part1}</span>
                                <span class="text-gray-600">${part2}</span>
                            </li>`;
                    }).join('');

                    vtlinksList.innerHTML = data.result.vtl.map(vtlink => {
                        const [part1, part2] = vtlink.split("\n\n");
                        return `
                            <li class="link-item mb-2">
                                <span class="font-semibold text-green-600">${part1}</span>
                                <span class="text-gray-600">${part2}</span>
                            </li>`;
                    }).join('');

                    document.getElementById('content').style.display = 'block';
                    linksList.style.display = 'none';
                    vtlinksList.style.display = 'none';
                    document.getElementById('backButton').style.display = 'block'; // Show the back button
                } else {
                    setTimeout(checkStatus, 1000); // Poll every second
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        document.getElementById('toggleLinksButton').addEventListener('click', () => {
            const linksList = document.getElementById('linksList');
            if (linksList.style.display === 'none') {
                linksList.style.display = 'block';
                document.getElementById('toggleLinksButton').innerText = 'Hide Links';
            } else {
                linksList.style.display = 'none';
                document.getElementById('toggleLinksButton').innerText = 'Show Links';
            }
        });

        document.getElementById('toggleLinksvButton').addEventListener('click', () => {
            const vtlinksList = document.getElementById('vtlinksList');
            if (vtlinksList.style.display === 'none') {
                vtlinksList.style.display = 'block';
                document.getElementById('toggleLinksvButton').innerText = 'Hide Virus Total Links';
            } else {
                vtlinksList.style.display = 'none';
                document.getElementById('toggleLinksvButton').innerText = 'Show Virus Total Links';
            }
        });

        document.getElementById('backButton').addEventListener('click', () => {
            window.location.href = '/emails'; // Redirect to the email list page
        });

        window.onload = checkStatus;
    </script>

    <script src="https://unpkg.com/flowbite@1.5.1/dist/flowbite.bundle.js"></script>
</body>

</html>-->

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Analysis</title>
    <link href="https://unpkg.com/flowbite@1.5.1/dist/flowbite.min.css" rel="stylesheet" />
    <style>
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        #content {
            max-height: 80vh;
            /* Set max height for scrolling */
            overflow-y: auto;
            /* Enable vertical scrolling */
        }

        .link-item {
            word-wrap: break-word;
            /* Ensure links wrap to the next line */
        }
    </style>
</head>

<body class="bg-gray-200 h-screen flex items-center justify-center overflow-y-auto">
    <!-- Loading Section -->
    <div id="loading" class="text-center bg-white p-6 border border-gray-300 rounded-lg shadow-md">
        <div class="spinner"></div>
        <h1 class="text-3xl font-semibold text-gray-700">Loading...</h1>
    </div>

    <!-- Content Section -->
    <div id="content" style="display: none;" class="p-6 w-full max-w-4xl bg-white border border-gray-200 rounded-lg shadow-lg">
        <h2 class="text-xl font-bold mb-2">Body:</h2>
        <h3 id="bodyText" class="mb-4 text-gray-600"></h3>

        <h2 class="text-xl font-bold mb-2">VirusTotal:</h2>
        <h3 id="linkvt" class="mb-4 text-gray-600"></h3>

        <button id="toggleRawBodyButton" class="block w-full text-center bg-purple-600 text-white py-2 px-4 mb-4 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium transition duration-150">
            Show Raw Body
        </button>
        <p id="rawbody" class="mb-4 text-gray-600" style="display: none;">
        </p>

        <!-- Toggle Links Buttons -->
        <button id="toggleLinksButton" class="block w-full text-center bg-purple-600 text-white py-2 px-4 mb-4 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium transition duration-150">
            Show Links
        </button>
        <ul id="linksList" class="list-disc list-inside bg-gray-100 p-4 rounded-lg max-h-64 overflow-y-auto" style="display: none;">
        </ul>

        <button id="toggleLinksvButton" class="block w-full text-center bg-purple-600 text-white py-2 px-4 mb-4 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium transition duration-150">
            Show Virus Total Links
        </button>
        <ul id="vtlinksList" class="list-disc list-inside bg-gray-100 p-4 rounded-lg max-h-64 overflow-y-auto" style="display: none;">
        </ul>

        <!-- Back Button -->
        <button id="backButton" style="display: none;" class="block w-full text-center bg-purple-600 text-white py-2 px-4 mb-4 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium transition duration-150">
            Back to Email List
        </button>
    </div>

    <script>
        async function checkStatus() {
            try {
                const response = await fetch('/check_status');
                const data = await response.json();
                if (data.status === 'done') {
                    document.getElementById('loading').style.display = 'none';

                    // Populate the body text and list
                    document.getElementById('bodyText').innerText = data.result.body;
                    document.getElementById('linkv').innerText = "VERDICT: " + data.result.linkv;
                    document.getElementById('linkvt').innerText = "VERDICT: " + data.result.linkvt;

                    const rawbody = document.getElementById('rawbody');
                    const linksList = document.getElementById('linksList');
                    const vtlinksList = document.getElementById('vtlinksList');
                    linksList.innerHTML = data.result.links.map(link => {
                        const [part1, part2] = link.split("\n\n");
                        return `
                            <li class="link-item mb-2">
                                <span class="font-semibold text-blue-600">${part1}</span>
                                <span class="text-gray-600">${part2}</span>
                            </li>
                        `;
                    }).join('');

                    rawbody.innerHTML = data.result.rawbody;

                    vtlinksList.innerHTML = data.result.vtl.map(vtlink => {
                        const [part1, part2] = vtlink.split("\n\n");
                        return `
                            <li class="link-item mb-2">
                                <span class="font-semibold text-green-600">${part1}</span>
                                <span class="text-gray-600">${part2}</span>
                            </li>
                        `;
                    }).join('');

                    document.getElementById('content').style.display = 'block';
                    rawbody.style.display = 'none';
                    linksList.style.display = 'none';
                    vtlinksList.style.display = 'none';
                    document.getElementById('backButton').style.display = 'block';
                } else {
                    setTimeout(checkStatus, 1000); // Poll every second
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        document.getElementById('toggleRawBodyButton').addEventListener('click', () => {
            const rawBodyContent = document.getElementById('rawbody');
            if (rawBodyContent.style.display === 'none') {
                rawBodyContent.style.display = 'block';
                document.getElementById('toggleRawBodyButton').innerText = 'Hide Raw Body';
            } else {
                rawBodyContent.style.display = 'none';
                document.getElementById('toggleRawBodyButton').innerText = 'Show Raw Body';
            }
        });

        document.getElementById('toggleLinksButton').addEventListener('click', () => {
            const linksList = document.getElementById('linksList');
            if (linksList.style.display === 'none') {
                linksList.style.display = 'block';
                document.getElementById('toggleLinksButton').innerText = 'Hide Links';
            } else {
                linksList.style.display = 'none';
                document.getElementById('toggleLinksButton').innerText = 'Show Links';
            }
        });

        document.getElementById('toggleLinksvButton').addEventListener('click', () => {
            const vtlinksList = document.getElementById('vtlinksList');
            if (vtlinksList.style.display === 'none') {
                vtlinksList.style.display = 'block';
                document.getElementById('toggleLinksvButton').innerText = 'Hide Virus Total Links';
            } else {
                vtlinksList.style.display = 'none';
                document.getElementById('toggleLinksvButton').innerText = 'Show Virus Total Links';
            }
        });

        document.getElementById('backButton').addEventListener('click', () => {
            window.location.href = '/emails'; // Redirect to the email list page
        });

        window.onload = checkStatus;
    </script>

    <script src="https://unpkg.com/flowbite@1.5.1/dist/flowbite.bundle.js"></script>
</body>

</html>
