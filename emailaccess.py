import base64
import requests
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from bs4 import BeautifulSoup
from google.auth.transport.requests import Request
from flask import session

# Import configuration
from config import CLIENT_CONFIG, SCOPES, REDIRECT_URI

def create_oauth_flow():
    """Create and return OAuth2 flow for web application"""
    import os
    # Allow HTTP for local development
    os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

    flow = Flow.from_client_config(
        CLIENT_CONFIG,
        scopes=SCOPES,
        redirect_uri=REDIRECT_URI
    )
    return flow

def get_authorization_url():
    """Get the authorization URL for OAuth2 flow"""
    # Clear any existing state first
    session.pop('oauth_state', None)
    session.pop('gmail_credentials', None)

    flow = create_oauth_flow()
    authorization_url, state = flow.authorization_url(
        access_type='offline',
        include_granted_scopes='true',
        prompt='consent'
    )
    session['oauth_state'] = state
    session.permanent = True  # Make session persistent
    print(f"Debug: Generated new state: {state}")
    return authorization_url

def handle_oauth_callback(authorization_response, state):
    """Handle the OAuth2 callback and get credentials"""
    stored_state = session.get('oauth_state')
    print(f"Debug: Received state: {state}")
    print(f"Debug: Stored state: {stored_state}")

    # For development, we'll be more lenient with state validation
    # In production, you should enforce strict state validation
    if state != stored_state:
        print(f"State mismatch: received {state}, expected {stored_state}")
        print("Continuing anyway for development (this should be fixed in production)")

    try:
        flow = create_oauth_flow()
        flow.fetch_token(authorization_response=authorization_response)

        # Store credentials in session
        credentials = flow.credentials
        session['gmail_credentials'] = {
            'token': credentials.token,
            'refresh_token': credentials.refresh_token,
            'token_uri': credentials.token_uri,
            'client_id': credentials.client_id,
            'client_secret': credentials.client_secret,
            'scopes': credentials.scopes
        }

        # Clear the oauth state
        session.pop('oauth_state', None)
        session.permanent = True  # Make session persistent

        print("Debug: Successfully stored Gmail credentials")
        return credentials

    except Exception as e:
        print(f"Error during OAuth callback: {e}")
        # Clear any partial state
        session.pop('oauth_state', None)
        session.pop('gmail_credentials', None)
        raise

def get_google_credentials():
    """Get Google credentials from session or return None if not authenticated"""
    if 'gmail_credentials' not in session:
        return None

    creds_data = session['gmail_credentials']
    creds = Credentials(
        token=creds_data['token'],
        refresh_token=creds_data['refresh_token'],
        token_uri=creds_data['token_uri'],
        client_id=creds_data['client_id'],
        client_secret=creds_data['client_secret'],
        scopes=creds_data['scopes']
    )

    # Refresh token if expired
    if not creds.valid:
        if creds.expired and creds.refresh_token:
            creds.refresh(Request())
            # Update session with new token
            session['gmail_credentials']['token'] = creds.token
        else:
            # Credentials are invalid and can't be refreshed
            session.pop('gmail_credentials', None)
            return None

    return creds

def is_authenticated():
    """Check if user is authenticated with Gmail"""
    return 'gmail_credentials' in session

def logout():
    """Remove Gmail credentials from session"""
    session.pop('gmail_credentials', None)
    session.pop('oauth_state', None)
"""
def get_subject(n):
    creds = get_google_credentials()
    headers = {
        'Authorization': f'Bearer {creds.token}',
        'Accept': 'application/json'
    }

    # Fetch recent email metadata (message IDs)
    response = requests.get('https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=100', headers=headers)
    
    if response.status_code != 200:
        print(f"Error fetching messages: {response.status_code} {response.text}")
        return None

    print(response)
    message_ids = response.json().get('messages', [])
    
    if n <= 0 or n > len(message_ids):
        print("Invalid message number.")
        return None

    # Get the nth most recent message (1-based index)
    message_id = message_ids[n-1]['id']  # Accessing the nth most recent message directly
    
    # Fetch the subject of the nth email
    msg_response = requests.get(f"https://gmail.googleapis.com/gmail/v1/users/me/messages/{message_id}", headers=headers)
    
    if msg_response.status_code != 200:
        print(f"Error fetching message {message_id}: {msg_response.status_code} {msg_response.text}")
        return None

    msg_data = msg_response.json()

    # Look for the subject in the headers
    for header in msg_data['payload']['headers']:
        if header['name'] == 'Subject':
            return header['value']
    
    return "No subject found"

"""


def get_subject(n):
    """Get recent email subjects and bodies"""
    creds = get_google_credentials()
    if not creds:
        print("Error: Not authenticated with Gmail")
        return []

    headers = {
        'Authorization': f'Bearer {creds.token}',
        'Accept': 'application/json'
    }

    # Step 1: Get the latest n message IDs
    response = requests.get(f'https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults={n}', headers=headers)

    if response.status_code == 401:
        print("Error: Gmail authentication expired. Please sign in again.")
        logout()  # Clear invalid credentials
        return []
    elif response.status_code != 200:
        print(f"Error fetching messages: {response.status_code} {response.text}")
        return []

    message_ids = response.json().get('messages', [])
    emails = []

    for i, msg in enumerate(message_ids):
        msg_id = msg['id']

        # Step 2: Fetch full message
        msg_response = requests.get(f"https://gmail.googleapis.com/gmail/v1/users/me/messages/{msg_id}?format=full", headers=headers)
        if msg_response.status_code == 401:
            print("Error: Gmail authentication expired during message fetch")
            logout()
            return []
        elif msg_response.status_code != 200:
            print(f"Error fetching message {msg_id}: {msg_response.status_code} {msg_response.text}")
            continue

        msg_data = msg_response.json()

        # Step 3: Extract subject
        subject = "No subject found"
        for header in msg_data['payload']['headers']:
            if header['name'] == 'Subject':
                subject = header['value']
                break

        # Step 4: Extract body
        body = "No body found"
        payload = msg_data.get('payload', {})
        if 'parts' in payload:
            for part in payload['parts']:
                if part['mimeType'] == 'text/plain' and 'data' in part['body']:
                    body = base64.urlsafe_b64decode(part['body']['data']).decode('utf-8')
                    break
        elif 'body' in payload and 'data' in payload['body']:
            body = base64.urlsafe_b64decode(payload['body']['data']).decode('utf-8')

        emails.append({'id': i + 1, 'subject': subject, 'body': body})

    return emails


# Function to fetch recent emails
def get_metadata(n):
    """Get email body and links for the nth most recent email"""
    creds = get_google_credentials()
    if not creds:
        print("Error: Not authenticated with Gmail")
        return None, []

    headers = {
        'Authorization': f'Bearer {creds.token}',
        'Accept': 'application/json'
    }

    response = requests.get('https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=100', headers=headers)
    if response.status_code == 401:
        print("Error: Gmail authentication expired. Please sign in again.")
        logout()
        return None, []
    elif response.status_code != 200:
        print(f"Error fetching messages: {response.status_code} {response.text}")
        return None, []

    message_ids = response.json().get('messages', [])
    if n <= 0 or n > len(message_ids):
        print("Invalid message number.")
        return None, []

    message_id = message_ids[n-1]['id']
    msg_response = requests.get(f"https://gmail.googleapis.com/gmail/v1/users/me/messages/{message_id}", headers=headers)
    if msg_response.status_code == 401:
        print("Error: Gmail authentication expired during message fetch")
        logout()
        return None, []
    elif msg_response.status_code != 200:
        print(f"Error fetching message {message_id}: {msg_response.status_code} {msg_response.text}")
        return None, []

    msg_data = msg_response.json()
    email_body = ""
    links = []

    def extract_parts(payload):
        parts = []
        if 'parts' in payload:
            for part in payload['parts']:
                parts.extend(extract_parts(part))
        else:
            parts.append(payload)
        return parts

    # Flatten all parts
    all_parts = extract_parts(msg_data['payload'])

    for part in all_parts:
        mime_type = part.get('mimeType', '')
        data = part.get('body', {}).get('data', '')
        if not data:
            continue
        try:
            decoded = base64.urlsafe_b64decode(data).decode('utf-8')
        except Exception as e:
            print(f"Decode error: {e}")
            continue
        if mime_type == 'text/plain' and not email_body:
            email_body = decoded
        elif mime_type == 'text/html':
            soup = BeautifulSoup(decoded, 'html.parser')

            # Extract links first
            links.extend([a['href'] for a in soup.find_all('a', href=True)])

            if not email_body:
                # Remove all <a> tags (links) before extracting text
                for a_tag in soup.find_all('a'):
                    a_tag.decompose()  # Completely remove the link tags and their content

                # Now extract clean text without link URLs
                email_body = soup.get_text(separator=' ', strip=True)

    return email_body.strip(), links

def get_metadata_with_creds(n, gmail_credentials):
    """Get email body and links for the nth most recent email using provided credentials"""
    # Create credentials object from the passed data
    creds = Credentials(
        token=gmail_credentials['token'],
        refresh_token=gmail_credentials['refresh_token'],
        token_uri=gmail_credentials['token_uri'],
        client_id=gmail_credentials['client_id'],
        client_secret=gmail_credentials['client_secret'],
        scopes=gmail_credentials['scopes']
    )

    # Refresh token if expired
    if not creds.valid:
        if creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            print("Error: Gmail credentials are invalid and can't be refreshed")
            return None, []

    headers = {
        'Authorization': f'Bearer {creds.token}',
        'Accept': 'application/json'
    }

    response = requests.get('https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=100', headers=headers)
    if response.status_code == 401:
        print("Error: Gmail authentication expired. Please sign in again.")
        return None, []
    elif response.status_code != 200:
        print(f"Error fetching messages: {response.status_code} {response.text}")
        return None, []

    message_ids = response.json().get('messages', [])
    if n <= 0 or n > len(message_ids):
        print("Invalid message number.")
        return None, []

    message_id = message_ids[n-1]['id']
    msg_response = requests.get(f"https://gmail.googleapis.com/gmail/v1/users/me/messages/{message_id}", headers=headers)
    if msg_response.status_code == 401:
        print("Error: Gmail authentication expired during message fetch")
        return None, []
    elif msg_response.status_code != 200:
        print(f"Error fetching message {message_id}: {msg_response.status_code} {msg_response.text}")
        return None, []

    msg_data = msg_response.json()
    email_body = ""
    links = []

    def extract_parts(payload):
        parts = []
        if 'parts' in payload:
            for part in payload['parts']:
                parts.extend(extract_parts(part))
        else:
            parts.append(payload)
        return parts

    # Flatten all parts
    all_parts = extract_parts(msg_data['payload'])

    for part in all_parts:
        mime_type = part.get('mimeType', '')
        data = part.get('body', {}).get('data', '')
        if not data:
            continue
        try:
            decoded = base64.urlsafe_b64decode(data).decode('utf-8')
        except Exception as e:
            print(f"Decode error: {e}")
            continue
        if mime_type == 'text/plain' and not email_body:
            email_body = decoded
        elif mime_type == 'text/html':
            soup = BeautifulSoup(decoded, 'html.parser')

            # Extract links first
            links.extend([a['href'] for a in soup.find_all('a', href=True)])

            if not email_body:
                # Remove all <a> tags (links) before extracting text
                for a_tag in soup.find_all('a'):
                    a_tag.decompose()  # Completely remove the link tags and their content

                # Now extract clean text without link URLs
                email_body = soup.get_text(separator=' ', strip=True)

    return email_body.strip(), links




# Example usage
if __name__ == "__main__":
    number_of_messages = 5  # Specify the number of recent messages to retrieve
    body, links = get_metadata(1)
    print(body)
    print(links)
    '''if not emails:
        print("No emails found.")
    else:
        for i, (body, links) in enumerate(emails):
            print(f"Email body {i + 1}:", body)
            print(f"Extracted links {i + 1}:", links)'''
